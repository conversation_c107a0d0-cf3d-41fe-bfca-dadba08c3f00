// Database initialization utility
// أداة تهيئة قاعدة البيانات

// This file ensures that the database is properly initialized
// when the admin panel starts up

import { initDatabase, clearDatabase } from '../../../database/database.js'
import {
  getHeroData,
  getWhyChooseUsData,
  getKitchensData,
  getCabinetsData,
  getFooterData,
  getCategories,
  getCompanySettings
} from '../../../database/api.js'

// Initialize all database collections with default data
export const initializeDatabase = async () => {
  try {
    console.log('🔄 Initializing database...')

    // First, initialize the SQLite database
    await initDatabase()
    console.log('✅ SQLite database initialized')

    // Then, initialize all data by calling get functions
    // This will create default data if none exists
    await Promise.all([
      getHeroData(),
      getWhyChooseUsData(),
      getKitchensData(),
      getCabinetsData(),
      getFooterData(),
      getCategories(),
      getCompanySettings()
    ])

    console.log('✅ Database initialized successfully')
    return true
  } catch (error) {
    console.error('❌ Failed to initialize database:', error)
    return false
  }
}

// Check if database has been initialized
export const isDatabaseInitialized = () => {
  try {
    // Check if basic data exists in localStorage
    const heroData = localStorage.getItem('db_hero_section')
    const categoriesData = localStorage.getItem('db_categories')
    
    return !!(heroData && categoriesData)
  } catch (error) {
    console.error('Error checking database initialization:', error)
    return false
  }
}

// Reset database to default state
export const resetDatabase = () => {
  try {
    console.log('🔄 Resetting database...')
    
    // Clear all database storage
    const dbKeys = [
      'db_hero_section',
      'db_why_choose_us',
      'db_kitchens',
      'db_cabinets',
      'db_footer',
      'db_categories',
      'db_company_settings',
      'db_activity_log'
    ]
    
    dbKeys.forEach(key => {
      localStorage.removeItem(key)
    })
    
    console.log('✅ Database reset successfully')
    return true
  } catch (error) {
    console.error('❌ Failed to reset database:', error)
    return false
  }
}

// Export database status
export const getDatabaseStatus = () => {
  try {
    const status = {
      initialized: isDatabaseInitialized(),
      collections: {}
    }
    
    // Check each collection
    const collections = [
      'db_hero_section',
      'db_why_choose_us',
      'db_kitchens',
      'db_cabinets',
      'db_footer',
      'db_categories',
      'db_company_settings'
    ]
    
    collections.forEach(collection => {
      const data = localStorage.getItem(collection)
      status.collections[collection] = {
        exists: !!data,
        size: data ? data.length : 0
      }
    })
    
    return status
  } catch (error) {
    console.error('Error getting database status:', error)
    return { initialized: false, collections: {}, error: error.message }
  }
}

// Force reset database and recreate with new data
export const forceResetDatabase = async () => {
  try {
    console.log('🔄 Force resetting database...')

    // Clear the database storage
    clearDatabase()

    // Reinitialize the database
    await initializeDatabase()

    console.log('✅ Database force reset completed')
    return true
  } catch (error) {
    console.error('❌ Failed to force reset database:', error)
    return false
  }
}

export default {
  initializeDatabase,
  isDatabaseInitialized,
  resetDatabase,
  getDatabaseStatus,
  forceResetDatabase
}
