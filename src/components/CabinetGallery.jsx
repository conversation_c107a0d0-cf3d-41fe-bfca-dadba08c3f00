import { useRef, useState, useEffect } from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation, Pagination } from 'swiper/modules';
import { motion, useInView, AnimatePresence } from 'framer-motion';
import { getCabinetsData, getFooterData } from '../database/api.js';

// Import Swiper styles
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';

const CabinetGallery = () => {
  const sectionRef = useRef(null);
  const [lightboxImage, setLightboxImage] = useState(null);
  const [modalImageIndex, setModalImageIndex] = useState(0);
  const isInView = useInView(sectionRef, { once: true, threshold: 0.1 });

  // البيانات الافتراضية (نفس البيانات الموجودة في DataContext)
  const defaultCabinets = [
    {
      id: 1,
      title: 'خزانة ملابس عصرية',
      description: 'تصميم عصري مع تنظيم داخلي مثالي وإضاءة LED',
      category: 'modern',
      images: [
        'https://readdy.ai/api/search-image?query=modern%20wardrobe%20closet%20design%2C%20sliding%20doors%2C%20organized%20storage%2C%20elegant%20interior%2C%20professional%20photography&width=600&height=800&seq=8&orientation=portrait'
      ]
    }
  ];

  const [cabinets, setCabinets] = useState(defaultCabinets);
  const [whatsappNumber, setWhatsappNumber] = useState('');

  // قراءة البيانات من قاعدة البيانات عند تحميل المكون
  useEffect(() => {
    const loadCabinetsData = async () => {
      try {
        const cabinetsData = await getCabinetsData();
        if (cabinetsData && cabinetsData.length > 0) {
          // تحويل البيانات من قاعدة البيانات إلى التنسيق المطلوب
          const formattedCabinets = cabinetsData.map(cabinet => ({
            id: cabinet.id,
            title: cabinet.title,
            description: cabinet.description,
            category: cabinet.category_slug || 'modern',
            images: cabinet.images?.map(img => img.image_url) || []
          }));
          setCabinets(formattedCabinets);
        } else {
          setCabinets(defaultCabinets);
        }
      } catch (error) {
        console.error('Error loading cabinets data:', error);
        setCabinets(defaultCabinets);
      }
    };

    const loadWhatsappNumber = async () => {
      try {
        const footerData = await getFooterData();
        if (footerData && footerData.contactInfo) {
          const phoneContact = footerData.contactInfo.find(contact => contact.icon === 'ri-phone-line');
          if (phoneContact) {
            setWhatsappNumber(phoneContact.text);
          }
        }
      } catch (error) {
        console.error('Error loading footer data:', error);
      }
    };

    // تحميل البيانات عند بداية المكون
    loadCabinetsData();
    loadWhatsappNumber();

    // إعداد تحديث دوري للبيانات (كل 30 ثانية)
    const interval = setInterval(() => {
      loadCabinetsData();
      loadWhatsappNumber();
    }, 30000);

    // تنظيف الـ interval عند إزالة المكون
    return () => {
      clearInterval(interval);
    };
  }, []);

  const categories = [
    { id: 'all', name: 'جميع التصاميم', icon: 'ri-apps-line' },
    { id: 'modern', name: 'عصري', icon: 'ri-building-line' },
    { id: 'classic', name: 'كلاسيكي', icon: 'ri-home-heart-line' },
    { id: 'luxury', name: 'فاخر', icon: 'ri-vip-crown-line' },
    { id: 'minimal', name: 'مينيمال', icon: 'ri-layout-line' }
  ];

  const filteredCabinets = cabinets;

  // دالة لفتح WhatsApp مع رابط المنتج
  const openWhatsApp = (cabinet) => {
    const currentUrl = window.location.href;
    const productUrl = `${currentUrl}#cabinet-${cabinet.id}`;
    const message = `مرحباً، أنا مهتم بهذه الخزانة: ${cabinet.title}\n\nرابط المنتج: ${productUrl}`;
    const phoneNumber = whatsappNumber.replace(/[^0-9]/g, ''); // إزالة أي رموز غير رقمية
    const whatsappUrl = `https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`;
    window.open(whatsappUrl, '_blank');
  };

  return (
    <section id="cabinets" className="relative py-24 bg-gradient-to-br from-slate-50 via-white to-purple-50 overflow-hidden" ref={sectionRef}>
      <div className="container mx-auto px-6 relative z-10">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 50 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
          transition={{ duration: 0.8 }}
        >
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-purple-500 to-blue-500 rounded-2xl mb-6 shadow-lg">
            <i className="ri-archive-line text-2xl text-white"></i>
          </div>
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold bg-gradient-to-r from-gray-800 via-gray-900 to-gray-800 bg-clip-text text-transparent mb-6">
            معرض
            <span className="block bg-gradient-to-r from-purple-600 via-blue-600 to-indigo-600 bg-clip-text text-transparent">
              الخزائن الأنيقة
            </span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            تصاميم خزائن عصرية وأنيقة تجمع بين الجمال والوظيفة لتلبية احتياجات التخزين مع الحفاظ على جمالية المكان
          </p>
          <div className="w-24 h-1 bg-gradient-to-r from-purple-500 to-blue-500 mx-auto mt-8 rounded-full"></div>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {filteredCabinets.map((cabinet, index) => (
            <motion.div
              key={cabinet.id}
              className="group relative bg-white rounded-3xl overflow-hidden shadow-xl hover:shadow-2xl transition-all duration-500 cursor-pointer"
              whileHover={{ y: -10 }}
              onClick={() => setLightboxImage(cabinet)}
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
            >
              <div className="relative h-80 md:h-96 overflow-hidden">
                <img
                  src={cabinet.images[0]}
                  alt={cabinet.title}
                  className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div className="absolute top-4 left-4 bg-white/90 backdrop-blur-sm text-gray-800 px-3 py-1 rounded-full text-xs font-medium">
                  {categories.find(cat => cat.id === cabinet.category)?.name}
                </div>
                <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                  <div className="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center border border-white/30">
                    <i className="ri-zoom-in-line text-2xl text-white"></i>
                  </div>
                </div>
              </div>
              <div className="p-6">
                <h3 className="text-xl font-bold text-gray-800 mb-2 group-hover:text-purple-600 transition-colors duration-300">
                  {cabinet.title}
                </h3>
                <p className="text-gray-600 text-sm mb-4 leading-relaxed">
                  {cabinet.description}
                </p>
                <div className="flex flex-wrap gap-2 mb-4">
                  {cabinet.features?.map((feature, idx) => (
                    <span
                      key={idx}
                      className="px-3 py-1 bg-gray-100 text-gray-700 text-xs rounded-full font-medium"
                    >
                      {feature}
                    </span>
                  ))}
                </div>
                <button className="w-full bg-gradient-to-r from-purple-500 to-blue-500 text-white py-3 rounded-xl font-medium hover:shadow-lg transition-all duration-300">
                  <div className="flex items-center justify-center space-x-2 rtl:space-x-reverse">
                    <i className="ri-eye-line"></i>
                    <span>عرض التفاصيل</span>
                  </div>
                </button>
              </div>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Enhanced Modal - Mobile First, Desktop Optimized */}
      <AnimatePresence>
        {lightboxImage && (
          <motion.div
            className="fixed inset-0 bg-black/90 backdrop-blur-sm z-50 flex items-center justify-center"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => {
              setLightboxImage(null);
              setModalImageIndex(0);
            }}
          >
            {/* Mobile Modal */}
            <motion.div
              className="lg:hidden relative bg-white w-full h-full flex flex-col shadow-2xl"
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.8, opacity: 0 }}
              onClick={(e) => e.stopPropagation()}
            >
              {/* Mobile Close Button */}
              <motion.button
                className="absolute top-4 right-4 z-20 w-12 h-12 bg-black/50 backdrop-blur-sm text-white rounded-full flex items-center justify-center"
                onClick={() => {
                  setLightboxImage(null);
                  setModalImageIndex(0);
                }}
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <i className="ri-close-line text-2xl"></i>
              </motion.button>

              {/* Mobile Image Slider */}
              <div className="relative flex-1 bg-black">
                <Swiper
                  modules={[Navigation, Pagination]}
                  spaceBetween={0}
                  slidesPerView={1}
                  navigation={{
                    nextEl: '.mobile-cabinet-swiper-button-next',
                    prevEl: '.mobile-cabinet-swiper-button-prev',
                  }}
                  pagination={{
                    clickable: true,
                    dynamicBullets: true,
                  }}
                  onSlideChange={(swiper) => setModalImageIndex(swiper.activeIndex)}
                  initialSlide={modalImageIndex}
                  className="mobile-cabinet-modal-swiper h-full"
                  style={{
                    '--swiper-pagination-color': '#8b5cf6',
                    '--swiper-pagination-bullet-inactive-color': '#d1d5db',
                  }}
                >
                  {lightboxImage.images.map((image, index) => (
                    <SwiperSlide key={index}>
                      <div className="relative h-full flex items-center justify-center">
                        <img
                          src={image}
                          alt={`${lightboxImage.title} - صورة ${index + 1}`}
                          className="max-w-full max-h-full object-contain"
                        />
                      </div>
                    </SwiperSlide>
                  ))}
                </Swiper>

                {/* Mobile Navigation Buttons */}
                <div className="mobile-cabinet-swiper-button-prev absolute left-4 top-1/2 -translate-y-1/2 z-10 w-10 h-10 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center shadow-lg cursor-pointer hover:bg-white/30 transition-all duration-300">
                  <i className="ri-arrow-right-line text-lg text-white"></i>
                </div>
                <div className="mobile-cabinet-swiper-button-next absolute right-4 top-1/2 -translate-y-1/2 z-10 w-10 h-10 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center shadow-lg cursor-pointer hover:bg-white/30 transition-all duration-300">
                  <i className="ri-arrow-left-line text-lg text-white"></i>
                </div>

                {/* Mobile Image Counter */}
                {lightboxImage.images.length > 1 && (
                  <div className="absolute bottom-4 left-4 bg-black/70 backdrop-blur-sm text-white px-3 py-1 rounded-full text-sm font-medium">
                    {modalImageIndex + 1} / {lightboxImage.images.length}
                  </div>
                )}
              </div>

              {/* Mobile Content Section */}
              <div className="p-6 bg-white max-h-[40vh] overflow-y-auto">
                <div className="text-center mb-6">
                  <h3 className="text-2xl font-bold text-gray-800 mb-4">
                    {lightboxImage.title}
                  </h3>
                  <p className="text-gray-600 leading-relaxed text-base">
                    {lightboxImage.description}
                  </p>
                </div>

                {/* Mobile Contact Section */}
                <div className="text-center">
                  <h4 className="text-xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent mb-6">
                    اطلب الآن
                  </h4>

                  <div className="grid grid-cols-2 gap-2 mb-6">
                    <motion.a
                      href="https://twitter.com/expertwonders"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center justify-center space-x-1 rtl:space-x-reverse bg-blue-500 text-white py-2 px-3 rounded-lg hover:bg-blue-600 transition-colors duration-300"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <i className="ri-twitter-line text-sm"></i>
                      <span className="text-xs font-medium">تويتر</span>
                    </motion.a>

                    <motion.a
                      href="https://snapchat.com/add/expertwonders"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center justify-center space-x-1 rtl:space-x-reverse bg-yellow-500 text-white py-2 px-3 rounded-lg hover:bg-yellow-600 transition-colors duration-300"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <i className="ri-snapchat-line text-sm"></i>
                      <span className="text-xs font-medium">سناب</span>
                    </motion.a>

                    <motion.a
                      href="https://instagram.com/expertwonders"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center justify-center space-x-1 rtl:space-x-reverse bg-pink-500 text-white py-2 px-3 rounded-lg hover:bg-pink-600 transition-colors duration-300"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <i className="ri-instagram-line text-sm"></i>
                      <span className="text-xs font-medium">انستا</span>
                    </motion.a>

                    <motion.button
                      onClick={() => openWhatsApp(lightboxImage)}
                      className="flex items-center justify-center space-x-1 rtl:space-x-reverse bg-green-500 text-white py-2 px-3 rounded-lg hover:bg-green-600 transition-colors duration-300"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <i className="ri-whatsapp-line text-sm"></i>
                      <span className="text-xs font-medium">واتساب</span>
                    </motion.button>

                    <motion.a
                      href="https://tiktok.com/@expertwonders"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center justify-center space-x-1 rtl:space-x-reverse bg-black text-white py-2 px-3 rounded-lg hover:bg-gray-800 transition-colors duration-300 col-span-2"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <i className="ri-tiktok-line text-sm"></i>
                      <span className="text-xs font-medium">تيك توك</span>
                    </motion.a>
                  </div>

                  {/* Mobile Thumbnail Images */}
                  {lightboxImage.images.length > 1 && (
                    <div className="mb-6">
                      <div className="grid grid-cols-3 gap-2">
                        {lightboxImage.images.map((image, index) => (
                          <button
                            key={index}
                            onClick={() => setModalImageIndex(index)}
                            className={`relative h-16 rounded-lg overflow-hidden border-2 transition-all duration-300 ${
                              modalImageIndex === index
                                ? 'border-purple-500 scale-105'
                                : 'border-gray-200 hover:border-purple-300'
                            }`}
                          >
                            <img
                              src={image}
                              alt={`${lightboxImage.title} - صورة ${index + 1}`}
                              className="w-full h-full object-cover"
                            />
                          </button>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </motion.div>

            {/* Desktop Modal */}
            <motion.div
              className="hidden lg:flex relative bg-white rounded-3xl overflow-hidden max-w-7xl w-full max-h-[90vh] shadow-2xl"
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.8, opacity: 0 }}
              onClick={(e) => e.stopPropagation()}
            >

              {/* Desktop Close Button */}
              <motion.button
                className="absolute top-6 right-6 z-20 w-12 h-12 bg-black/50 backdrop-blur-sm text-white rounded-full flex items-center justify-center hover:bg-black/70 transition-colors duration-300"
                onClick={() => {
                  setLightboxImage(null);
                  setModalImageIndex(0);
                }}
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <i className="ri-close-line text-xl"></i>
              </motion.button>

              {/* Desktop Image Section */}
              <div className="flex-[0.65] relative bg-black">
                <img
                  src={lightboxImage.images[modalImageIndex]}
                  alt={`${lightboxImage.title} - صورة ${modalImageIndex + 1}`}
                  className="w-full h-full object-contain"
                />

                {/* Desktop Image Counter */}
                {lightboxImage.images.length > 1 && (
                  <div className="absolute bottom-6 left-6 bg-black/70 backdrop-blur-sm text-white px-4 py-2 rounded-full text-sm font-medium">
                    {modalImageIndex + 1} / {lightboxImage.images.length}
                  </div>
                )}
              </div>

              {/* Desktop Content Section */}
              <div className="flex-[0.35] p-8 bg-gradient-to-br from-gray-50 to-white flex flex-col justify-between">
                <div>
                  <div className="text-center mb-8">
                    <h3 className="text-3xl font-bold text-gray-800 mb-6">
                      {lightboxImage.title}
                    </h3>
                    <p className="text-gray-600 leading-relaxed text-lg">
                      {lightboxImage.description}
                    </p>
                  </div>

                  {/* Desktop Contact Section */}
                  <div className="text-center">
                    <h4 className="text-2xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent mb-8">
                      اطلب الآن
                    </h4>

                    <div className="grid grid-cols-2 gap-4 mb-8">
                      <motion.a
                        href="https://twitter.com/expertwonders"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center justify-center space-x-3 rtl:space-x-reverse bg-blue-500 text-white py-4 px-6 rounded-xl hover:bg-blue-600 transition-colors duration-300 shadow-lg"
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        <i className="ri-twitter-line text-xl"></i>
                        <span className="text-base font-medium">تويتر</span>
                      </motion.a>

                      <motion.a
                        href="https://snapchat.com/add/expertwonders"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center justify-center space-x-3 rtl:space-x-reverse bg-yellow-500 text-white py-4 px-6 rounded-xl hover:bg-yellow-600 transition-colors duration-300 shadow-lg"
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        <i className="ri-snapchat-line text-xl"></i>
                        <span className="text-base font-medium">سناب شات</span>
                      </motion.a>

                      <motion.a
                        href="https://instagram.com/expertwonders"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center justify-center space-x-3 rtl:space-x-reverse bg-pink-500 text-white py-4 px-6 rounded-xl hover:bg-pink-600 transition-colors duration-300 shadow-lg"
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        <i className="ri-instagram-line text-xl"></i>
                        <span className="text-base font-medium">انستغرام</span>
                      </motion.a>

                      <motion.button
                        onClick={() => openWhatsApp(lightboxImage)}
                        className="flex items-center justify-center space-x-3 rtl:space-x-reverse bg-green-500 text-white py-4 px-6 rounded-xl hover:bg-green-600 transition-colors duration-300 shadow-lg"
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        <i className="ri-whatsapp-line text-xl"></i>
                        <span className="text-base font-medium">واتساب</span>
                      </motion.button>

                      <motion.a
                        href="https://tiktok.com/@expertwonders"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center justify-center space-x-3 rtl:space-x-reverse bg-black text-white py-4 px-6 rounded-xl hover:bg-gray-800 transition-colors duration-300 shadow-lg col-span-2"
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        <i className="ri-tiktok-line text-xl"></i>
                        <span className="text-base font-medium">تيك توك</span>
                      </motion.a>
                    </div>
                  </div>
                </div>

                {/* Desktop Thumbnail Images */}
                {lightboxImage.images.length > 1 && (
                  <div className="mt-6">
                    <div className="grid grid-cols-3 gap-3">
                      {lightboxImage.images.map((image, index) => (
                        <button
                          key={index}
                          onClick={() => setModalImageIndex(index)}
                          className={`relative h-20 rounded-xl overflow-hidden border-3 transition-all duration-300 ${
                            modalImageIndex === index
                              ? 'border-purple-500 scale-105 shadow-lg'
                              : 'border-gray-200 hover:border-purple-300 hover:scale-102'
                          }`}
                        >
                          <img
                            src={image}
                            alt={`${lightboxImage.title} - صورة ${index + 1}`}
                            className="w-full h-full object-cover"
                          />
                        </button>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </section>
  );
};

export default CabinetGallery;